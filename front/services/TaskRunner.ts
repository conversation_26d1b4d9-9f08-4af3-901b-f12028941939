import {ImageRecognitionService, YoloDetectionResult, YoloDetectionSuccessResponse} from "./ImageRecognitionService";
import {TaskStatusSocket} from "./websocket/TaskStatusSocket";
import {RobotArmSocket} from "./websocket/RobotArmSocket";
import {ClickQueueService} from "./ClickQueueService";
import {RobotArmMovementService} from "./RobotArmMovementService";
import {mobileLog, sleep} from "../utils/funs";
import {RobotArmService} from "./RobotArmService";

let taskRunner: TaskRunner|null;

export class TaskRunner {

    robotArmService: RobotArmService
    imageRecognitionService: ImageRecognitionService
    robotArmMovementService: RobotArmMovementService
    clickQueueService: ClickQueueService


    constructor() {


        this.robotArmService = RobotArmSocket.getRobotArmService();
        this.imageRecognitionService = new ImageRecognitionService();
        this.robotArmMovementService = new RobotArmMovementService();

        // 点击队列服务
        this.clickQueueService = new ClickQueueService(this.robotArmService);

    }


    static async startTask() {
        if (taskRunner) {
            return;
        }
        mobileLog('任务开始执行', 'info');
        taskRunner = new TaskRunner();

        await taskRunner.startWhile()
    }


    async startWhile() {
        while (TaskStatusSocket.getStatus()) {

            // 并行执行：处理点击队列的同时准备下一步操作
            await this.clickQueueService.runClickQueue();


            // 移动到指定位置
            await this.robotArmService.moveTo(this.robotArmMovementService.currX, this.robotArmMovementService.currY);

            // 开始异步拍照识别
            await sleep(600);// 等待摄像头画面稳定
            this.startAsyncRecognition();

            // 移动完成以后 计算机械臂下一个位置
            this.robotArmMovementService.calculateNextPosition();

        }
        taskRunner = null;
        mobileLog('任务循环结束', 'info');
    }



    /**
     * 异步开始识别过程
     */
    startAsyncRecognition() {
        // 记录当前机械臂坐标，用于后续计算点击位置
        const recognitionX = this.robotArmMovementService.currX;
        const recognitionY = this.robotArmMovementService.currY;

        mobileLog(`识别过程已启动，记录识别位置: X=${recognitionX}, Y=${recognitionY}`, 'info');

        // 执行识别
        this.imageRecognitionService.captureAndRecognize().then((recognitionResult: YoloDetectionSuccessResponse) => {
            this.processRecognitionResult(recognitionResult, recognitionX, recognitionY);
        });
    }


    /**
     * 处理识别结果
     */
    processRecognitionResult(recognitionResult: YoloDetectionSuccessResponse, recognitionX: number, recognitionY: number) {
        if (recognitionResult && recognitionResult.results) {
            mobileLog(`处理识别结果 [发现 ${recognitionResult.results.length} 个目标`, 'info');

            for (let i = 0; i < recognitionResult.results.length; i++) {
                // 将识别时的机械臂坐标附加到目标对象上
                const target: YoloDetectionResult = recognitionResult.results[i];
                target.currX = recognitionX;
                target.currY = recognitionY;

                this.clickQueueService.add(target);
            }
        } else {
            mobileLog(`识别结果为空`, 'info');
        }
    }

}