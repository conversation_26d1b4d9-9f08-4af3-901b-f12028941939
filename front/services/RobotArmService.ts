/**
 * 机械臂服务 RobotArmService.ts
 * 用于 uni-app 前端项目
 * 提供机械臂控制的完整 TypeScript 实现
 */

import config from "../config";
import { ConfigService } from "./ConfigService";
import { CountService } from "./CountService";
import { showToast, showModal, mobileLog, sleep } from "../utils/funs";

// ==================== 类型定义 ====================

/**
 * 配置对象接口
 */
interface Config {
    host: string;
    port: number;
    useSSL?: boolean;
    deviceIds?: string[];
    canvasResolution?: {
        x: number;
        y: number;
    };
}

/**
 * 位置坐标接口
 */
export interface Position {
    x: number;
    y: number;
}

/**
 * API 响应基础接口
 */
interface ApiResponse {
    success?: boolean;
    handle?: number;
    error?: string;
    message?: string;
}

/**
 * Toast 配置接口
 */
interface ToastOptions {
    title: string;
    icon?: 'success' | 'error' | 'loading' | 'none';
    duration?: number;
}

/**
 * Modal 配置接口
 */
interface ModalOptions {
    title: string;
    content?: string;
    showCancel?: boolean;
    confirmText?: string;
    cancelText?: string;
}

// ==================== 全局变量 ====================

const baseUrl = `https://${(config as Config).host}:${(config as Config).port}`;
let openHandleDebouncing = false; // 防抖处理

// ==================== 工具函数 ====================

/**
 * 统一的网络请求函数
 * @param url 请求URL
 * @returns Promise<any | null>
 */
async function _fetch(url: string): Promise<any | null> {
    try {
        const response = await fetch(url);

        if (!response.ok) {
            const errorMsg = `HTTP ${response.status}: 网络请求失败 ${response.statusText}`;
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return null;
        }

        return await response.json();

    } catch (error: any) {
        mobileLog(`网络请求失败: ${error.message}`);

        let errorMsg: string;
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorMsg = '无法连接到服务器，请检查网络连接';
        } else if (error.name === 'AbortError') {
            errorMsg = '请求超时，请重试';
        } else {
            errorMsg = `网络请求失败: ${error.message}`;
        }

        showModal({
            title: errorMsg,
            showCancel: false,
        } as ModalOptions);

        return null;
    }
}


// ==================== 机械臂服务类 ====================

/**
 * 机械臂服务类
 * 提供机械臂控制的完整实例方法
 */
export class RobotArmService {
    private readonly handle: number;
    private readonly port: string;
    private currentPosition: Position;

    /**
     * 构造函数
     * @param handle 机械臂句柄
     * @param port 端口名称
     */
    constructor(handle: number, port: string) {
        this.handle = handle;
        this.port = port;
        this.currentPosition = { x: 0, y: 0 };
    }




    /**
     * 打开端口，返回RobotArmService实例
     * @param port 端口名，如 'COM4'
     * @returns Promise<RobotArmService | null>
     */
    public static async openHandle(port: string = 'COM4'): Promise<RobotArmService | null> {
        // 防抖处理，防止连续点击
        if (openHandleDebouncing) {
            mobileLog('正在处理中，请稍候...');
            return null;
        }

        openHandleDebouncing = true;

        try {
            const url = `${baseUrl}/robot-arm/open?port=${encodeURIComponent(port)}`;
            const data: ApiResponse = await _fetch(url);

            if (data?.success && data.handle) {
                // 显示成功提示
                showToast({
                    title: '端口打开成功',
                    icon: 'success',
                    duration: 1500
                } as ToastOptions);

                mobileLog('端口打开成功');
                await sleep(200);

                // 返回RobotArmService实例
                return new RobotArmService(data.handle, port);
            } else {
                const errorMsg = `端口打开失败，返回值：${JSON.stringify(data)}`;
                showModal({
                    title: errorMsg,
                    showCancel: false,
                } as ModalOptions);
                return null;
            }
        } catch (error: any) {
            const errorMsg = `打开端口失败: ${error.message}`;
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return null;
        } finally {
            openHandleDebouncing = false;
        }
    }

    /**
     * 传入屏幕像素移动到机械臂指定位置
     * 将屏幕像素坐标转换为机械臂物理世界坐标
     * @param x 屏幕像素X坐标
     * @param y 屏幕像素Y坐标
     * @returns Promise<boolean> 移动是否成功
     */
    public async moveToByPx(x: number, y: number): Promise<boolean> {
        try {

            // 获取像素转换比例
            const pxWidth = CountService.getPxWidth();
            const pxHeight = CountService.getPxHeight();


            // 坐标转换
            let moveX = x / pxWidth;
            let moveY = y / pxHeight;

            // 添加偏移量
            moveX += ConfigService.getOffsetX();
            moveY += ConfigService.getOffsetY();

            // 向上取整确保精确定位
            moveX = Math.ceil(moveX);
            moveY = Math.ceil(moveY);

            mobileLog(`像素坐标转换: (${x}, ${y}) -> (${moveX}, ${moveY})`);

            return await this.moveTo(moveX, moveY);
        } catch (error: any) {
            mobileLog(`像素移动失败: ${error.message}`);
            showModal({
                title: `像素移动失败: ${error.message}`,
                showCancel: false,
            } as ModalOptions);
            return false;
        }
    }


    /**
     * 移动到指定坐标
     * @param x 机械臂X坐标
     * @param y 机械臂Y坐标
     * @returns Promise<boolean> 移动是否成功
     */
    public async moveTo(x: number, y: number): Promise<boolean> {
        try {

            // 对x和y进行四舍五入，确保是整数
            x = Math.round(x);
            y = Math.round(y);

            // 检查句柄是否有效
            if (!this.handle || this.handle <= 0) {
                showModal({
                    title: "机械臂端口未打开",
                    showCancel: false,
                } as ModalOptions);
                return false;
            }

            // 读取XY轴最大移动距离配置
            const maxMoveX = ConfigService.getMaxMoveX();
            const maxMoveY = ConfigService.getMaxMoveY();

            // 限制移动距离不超过最大值和最小值
            const limitedX = Math.min(Math.max(x, 0), maxMoveX);
            const limitedY = Math.min(Math.max(y, 0), maxMoveY);

            // 如果坐标被限制了，输出日志提示
            if (limitedX !== x || limitedY !== y) {
                const limitMsg = `移动坐标被限制: 原坐标(${x}, ${y}) -> 限制后坐标(${limitedX}, ${limitedY}), 最大移动距离: X=${maxMoveX}, Y=${maxMoveY}`;
                mobileLog(limitMsg);
                showToast({
                    title: '坐标已自动调整到安全范围',
                    icon: 'none',
                    duration: 2000
                } as ToastOptions);
            }

            // 计算移动距离
            const deltaX = Math.abs(limitedX - this.currentPosition.x);
            const deltaY = Math.abs(limitedY - this.currentPosition.y);
            const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            mobileLog(`从位置 (${this.currentPosition.x}, ${this.currentPosition.y}) 移动到 (${limitedX}, ${limitedY}), 移动距离: ${moveDistance.toFixed(2)}`);

            // 发送移动请求
            const url = `${baseUrl}/robot-arm/move?handle=${encodeURIComponent(this.handle)}&x=${limitedX}&y=${limitedY}`;
            const data: ApiResponse = await _fetch(url);

            if (data === null) {
                return false;
            }

            // 更新当前位置
            this.currentPosition = { x: limitedX, y: limitedY };

            // 根据移动距离计算需要等待的时间
            // 机械臂速度约0.19单位/ms，基础等待50ms确保稳定
            const waitTime = Math.max(50, Math.round(50 + moveDistance / 0.19));
            mobileLog(`移动到坐标 (${limitedX}, ${limitedY}) 成功，等待 ${waitTime}ms 确保移动到位`);

            // 等待机械臂移动到位
            await sleep(waitTime);

            return true;

        } catch (error: any) {
            const errorMsg = `移动失败: ${error.message}`;
            mobileLog(errorMsg);
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return false;
        }
    }

    /**
     * 按下（触控笔下降）
     * @param z 下降距离，默认8mm
     * @returns Promise<boolean> 按下是否成功
     */
    public async press(z: number = 8): Promise<boolean> {
        try {
            // 检查句柄是否有效
            if (!this.handle || this.handle <= 0) {
                showModal({
                    title: '机械臂端口未打开',
                    showCancel: false,
                } as ModalOptions);
                return false;
            }

            const url = `${baseUrl}/robot-arm/press?handle=${encodeURIComponent(this.handle)}&z=${z}`;
            const data: ApiResponse = await _fetch(url);

            if (data === null) {
                return false;
            }

            mobileLog(`按下成功，下降距离: ${z}mm`);
            await sleep(50);

            return true;

        } catch (error: any) {
            const errorMsg = `按下失败: ${error.message}`;
            mobileLog(errorMsg);
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return false;
        }
    }

    /**
     * 抬起（触控笔抬起）
     * @returns Promise<boolean> 抬起是否成功
     */
    public async release(): Promise<boolean> {
        try {
            // 检查句柄是否有效
            if (!this.handle || this.handle <= 0) {
                showModal({
                    title: '机械臂端口未打开',
                    showCancel: false,
                } as ModalOptions);
                return false;
            }

            const url = `${baseUrl}/robot-arm/release?handle=${encodeURIComponent(this.handle)}`;
            const data: ApiResponse = await _fetch(url);

            if (data === null) {
                return false;
            }

            mobileLog('抬起成功');

            // 等待机械臂移动到位
            await sleep(100);

            return true;

        } catch (error: any) {
            const errorMsg = `抬起失败: ${error.message}`;
            mobileLog(errorMsg);
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return false;
        }
    }

    /**
     * 点击（移动到坐标后按下再抬起）
     * @param x 目标X坐标
     * @param y 目标Y坐标
     * @param pressDepth 按下深度，默认8mm
     * @returns Promise<boolean> 点击是否成功
     */
    public async click(x: number, y: number, pressDepth: number = 8): Promise<boolean> {
        try {
            mobileLog(`开始点击坐标 (${x}, ${y})`);

            // 移动到指定位置
            const moveSuccess = await this.moveTo(x, y);
            if (!moveSuccess) {
                mobileLog('移动到目标位置失败');
                return false;
            }

            // 按下
            const pressSuccess = await this.press(pressDepth);
            if (!pressSuccess) {
                mobileLog('按下操作失败');
                return false;
            }

            // 抬起
            const releaseSuccess = await this.release();
            if (!releaseSuccess) {
                mobileLog('抬起操作失败');
                return false;
            }

            mobileLog(`点击坐标 (${x}, ${y}) 完成`);
            return true;

        } catch (error: any) {
            const errorMsg = `点击操作失败: ${error.message}`;
            mobileLog(errorMsg);
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return false;
        }
    }

    /**
     * 滑动（从起点滑到终点）
     * @param x1 起点X坐标
     * @param y1 起点Y坐标
     * @param x2 终点X坐标
     * @param y2 终点Y坐标
     * @param pressDepth 按下深度，默认8mm
     * @returns Promise<boolean> 滑动是否成功
     */
    public async swipe(x1: number, y1: number, x2: number, y2: number, pressDepth: number = 8): Promise<boolean> {
        try {

            mobileLog(`开始滑动: (${x1}, ${y1}) -> (${x2}, ${y2})`);

            // 移动到起点
            const moveToStartSuccess = await this.moveTo(x1, y1);
            if (!moveToStartSuccess) {
                mobileLog('移动到起点失败');
                return false;
            }

            // 按下
            const pressSuccess = await this.press(pressDepth);
            if (!pressSuccess) {
                mobileLog('按下操作失败');
                return false;
            }

            // 移动到终点
            const moveToEndSuccess = await this.moveTo(x2, y2);
            if (!moveToEndSuccess) {
                mobileLog('移动到终点失败');
                // 尝试抬起触控笔，避免卡住
                await this.release().catch(() => {});
                return false;
            }

            // 抬起
            const releaseSuccess = await this.release();
            if (!releaseSuccess) {
                mobileLog('抬起操作失败');
                return false;
            }

            mobileLog(`滑动完成: (${x1}, ${y1}) -> (${x2}, ${y2})`);
            return true;

        } catch (error: any) {
            const errorMsg = `滑动操作失败: ${error.message}`;
            mobileLog(errorMsg);

            // 尝试抬起触控笔，避免卡住
            try {
                await this.release();
            } catch (releaseError: any) {
                mobileLog(`滑动失败后抬起也失败: ${releaseError.message}`);
            }

            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);

            return false;
        }
    }

    /**
     * 回到零点（复位）
     * @returns Promise<boolean> 复位是否成功
     */
    public async reset(): Promise<boolean> {
        try {
            // 检查句柄是否有效
            if (!this.handle || this.handle <= 0) {
                showModal({
                    title: '机械臂端口未打开',
                    showCancel: false,
                } as ModalOptions);
                return false;
            }

            mobileLog('开始复位到零点');
            const success = await this.moveTo(0, 0);

            if (success) {
                mobileLog('复位成功');
                showToast({
                    title: '复位成功',
                    icon: 'success',
                    duration: 1500
                } as ToastOptions);
            }

            return success;
        } catch (error: any) {
            const errorMsg = `复位失败: ${error.message}`;
            mobileLog(errorMsg);
            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);
            return false;
        }
    }

    /**
     * 关闭端口
     * @returns Promise<boolean> 关闭是否成功
     */
    public async closeHandle(): Promise<boolean> {
        try {
            if (!this.handle || this.handle <= 0) {
                mobileLog('没有打开的端口需要关闭');
                return true;
            }

            mobileLog(`开始关闭端口，句柄: ${this.handle}`);
            const url = `${baseUrl}/robot-arm/close?handle=${encodeURIComponent(this.handle)}`;
            const data: ApiResponse = await _fetch(url);

            if (data === null) {
                // 网络错误时可能是正常断开，不返回false
                mobileLog('网络连接失败，可能端口已正常断开');
                return true;
            }

            mobileLog(`关闭端口结果: ${JSON.stringify(data)}`);


            // 显示成功提示
            showToast({
                title: '端口已关闭',
                icon: 'success',
                duration: 1500
            } as ToastOptions);

            return true;

        } catch (error: any) {
            const errorMsg = `关闭端口失败: ${error.message}`;
            mobileLog(errorMsg);

            showModal({
                title: errorMsg,
                showCancel: false,
            } as ModalOptions);

            return false;
        }
    }

}
